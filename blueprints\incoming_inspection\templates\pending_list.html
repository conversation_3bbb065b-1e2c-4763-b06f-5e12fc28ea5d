{% extends "base.html" %}

{% block title %}待检清单 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}{% endblock %}

{% block extra_css %}
<style>
    /* 采用与抽样检验记录页面相同的简洁风格 */

    /* 页面标题和操作按钮区域 - 与抽样检验记录一致 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .header-left {
        display: flex;
        align-items: center;
    }

    .header-left h1 {
        font-size: 18px;
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .header-right {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    /* 批量操作按钮样式 */
    .batch-actions {
        display: none;
        gap: 8px;
        align-items: center;
        margin-right: 8px;
    }

    .batch-actions.show {
        display: flex;
    }

    /* 搜索框样式 - 与抽样检验记录一致 */
    .quick-search-form {
        display: flex;
        align-items: center;
        margin-right: 10px;
        position: relative;
        border: 1px solid #1976d2;
        border-radius: 4px;
        background-color: #fff;
        height: 32px;
        width: 180px;
    }

    .quick-search-input {
        height: 32px;
        width: 180px;
        padding: 2px 8px;
        padding-right: 30px;
        border: none;
        font-size: 12px;
        outline: none;
        background: transparent;
        z-index: 1;
        max-width: none;
    }

    .search-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #1976d2;
        font-size: 14px;
        cursor: pointer;
        z-index: 2;
        pointer-events: auto;
    }

    /* 按钮样式 - 与抽样检验记录一致 */
    .buttons-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn {
        padding: 2px 5px;
        font-size: 11px;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        border-radius: 4px;
        border: 1px solid transparent;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
    }

    .btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .btn-primary {
        color: #fff;
        background-color: #1976d2;
        border-color: #1976d2;
    }

    .btn-primary:hover {
        background-color: #1565c0;
        border-color: #1565c0;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-sm {
        padding: 2px 6px;
        font-size: 11px;
        line-height: 1.5;
        border-radius: 3px;
    }

    /* 移除筛选区域相关样式 */

    /* 表格样式 - 与抽样检验记录完全一致 */
    .table-container {
        margin: 0;
    }

    .sortable-table {
        font-size: 11px;
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
    }

    .sortable-table th, .sortable-table td {
        padding: 3px 6px;
        white-space: nowrap;
        border: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;
    }

    .sortable-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .sortable-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* 复选框样式 */
    .sortable-table input[type="checkbox"] {
        transform: scale(1.1);
        cursor: pointer;
    }

    .sortable-table tbody tr.selected {
        background-color: #e3f2fd;
    }

    .sortable-table tbody tr.selected:hover {
        background-color: #bbdefb;
    }

    /* 模态框样式 */
    .modal {
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background-color: #fff;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #eee;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
    }

    .close {
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        color: #999;
    }

    .close:hover {
        color: #333;
    }

    .modal-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 16px;
    }

    .form-group label {
        display: block;
        margin-bottom: 4px;
        font-size: 12px;
        font-weight: 600;
        color: #333;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 12px;
        box-sizing: border-box;
    }

    .form-group textarea {
        height: 60px;
        resize: vertical;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        padding: 16px 20px;
        border-top: 1px solid #eee;
    }

    /* 状态标签样式 */
    .status-badge {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        min-width: 60px;
        display: inline-block;
    }

    /* 移除状态相关样式，待检清单只显示待检项目 */

    /* 操作按钮样式 */
    .action-buttons {
        display: flex;
        gap: 3px;
        flex-wrap: wrap;
    }

    .btn-warning {
        color: #fff;
        background-color: #ffc107;
        border-color: #ffc107;
    }

    .btn-warning:hover {
        background-color: #e0a800;
        border-color: #d39e00;
    }

    .btn-danger {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    /* 分页样式 - 与抽样检验记录完全一致 */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .pagination a, .pagination span {
        padding: 3px 6px;
        text-decoration: none;
        border: 1px solid #ddd;
        margin: 0 2px;
        color: #333;
        font-size: 11px;
    }

    .pagination a:hover {
        background-color: #f1f1f1;
    }

    .pagination .active {
        background-color: #1976d2;
        color: white;
        border: 1px solid #1976d2;
    }

    .pagination .disabled {
        color: #aaa;
        border: 1px solid #ddd;
    }

    /* 分页容器样式 */
    .pagination-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding: 5px 0;
    }

    .summary {
        margin-bottom: 5px;
        color: #666;
        font-size: 0.75em;
    }

    /* 简洁的空状态和加载状态 */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #666;
        font-size: 12px;
    }

    /* 移除独立的加载状态样式，统一在表格内显示 */

    /* 简洁的提示消息 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 4px;
        color: white;
        font-size: 12px;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .toast.show {
        opacity: 1;
    }

    .toast.success {
        background: #28a745;
    }

    .toast.error {
        background: #dc3545;
    }

    .toast.warning {
        background: #ffc107;
        color: #212529;
    }

    /* 移动端响应式 - 与抽样检验记录一致 */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .header-left {
            margin-bottom: 8px;
        }

        .header-right {
            width: 100%;
            flex-wrap: wrap;
        }

        .quick-search-form {
            width: 100%;
            margin-bottom: 8px;
        }

        .quick-search-input {
            width: 100%;
        }

        .buttons-container {
            width: 100%;
            justify-content: center;
        }

        .sortable-table {
            font-size: 10px;
        }

        .sortable-table th,
        .sortable-table td {
            padding: 2px 4px;
        }
    }
        
        .data-table th,
        .data-table td {
            padding: 8px 4px;
        }
</style>
{% endblock %}

{% block content %}
<!-- 采用与抽样检验记录页面相同的简洁布局 -->
<div class="page-header">
    <div class="header-left">
        <h1>待检清单 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}</h1>
    </div>
    <div class="header-right">
        <!-- 搜索框 -->
        <div class="quick-search-form">
            <input type="text" id="quick-search-input" class="quick-search-input" placeholder="请输入料号/名称/供应商进行搜索...">
            <i class="fas fa-search search-icon" id="search-icon"></i>
        </div>

        <!-- 批量操作按钮区域（默认隐藏） -->
        <div class="batch-actions" id="batch-actions" style="display: none;">
            <button type="button" class="btn btn-warning btn-sm" id="batch-edit-btn">
                <i class="fas fa-edit"></i> 编辑
            </button>
            <button type="button" class="btn btn-danger btn-sm" id="batch-delete-btn">
                <i class="fas fa-trash"></i> 删除
            </button>
        </div>

        <!-- 高级搜索按钮 -->
        <button type="button" class="btn btn-secondary btn-sm" id="advanced-search-btn" style="display: none;">
            <i class="fas fa-filter"></i> 高级搜索
        </button>

        <!-- 按钮区域 -->
        <div class="buttons-container">
            <a href="{{ url_for('incoming_inspection.batch_import_' + inspection_type) }}" class="btn btn-success">
                <i class="fas fa-upload"></i> 批量导入
            </a>
            {% if inspection_type == 'sampling' %}
            <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增检验
            </a>
            {% else %}
            <a href="{{ url_for('full_inspection.new_inspection') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增检验
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- 移除筛选区域，保持页面简洁 -->

<!-- 简洁的表格容器 - 与抽样检验记录一致 -->
<div class="table-container">
    <!-- 数据表格 - 添加复选框列 -->
    <table class="sortable-table" id="data-table">
        <thead>
            <tr>
                <th width="40">
                    <input type="checkbox" id="select-all" title="全选/取消全选">
                </th>
                <th data-sort="material_number">料号</th>
                <th data-sort="material_name">名称</th>
                <th data-sort="specification">规格</th>
                <th data-sort="supplier_name">供应商</th>
                <th data-sort="incoming_quantity">数量</th>
                <th data-sort="unit">单位</th>
                <th data-sort="batch_number">批次号</th>
                <th data-sort="created_at">创建时间</th>
                <th data-sort="inspector">检验员</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="data-tbody">
            <!-- 数据将在这里动态生成 -->
        </tbody>
    </table>

    <!-- 空状态 -->
    <div class="empty-state" id="empty-state" style="display: none;">
        <p>暂无待检物料，点击"批量导入"开始添加</p>
    </div>
</div>

<!-- 简洁的分页 -->
<div class="pagination-container" id="pagination-container" style="display: none;">
    <div class="summary" id="summary">显示第 1-20 条，共 0 条记录</div>
    <div class="pagination" id="pagination">
        <!-- 分页按钮将在这里动态生成 -->
    </div>
</div>

<!-- 批量编辑模态框 -->
<div id="batch-edit-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>批量编辑</h3>
            <span class="close" onclick="closeBatchEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <p>已选择 <span id="selected-count">0</span> 个项目</p>
            <form id="batch-edit-form">
                <div class="form-group">
                    <label>供应商：</label>
                    <input type="text" id="batch-supplier" placeholder="留空表示不修改">
                </div>
                <div class="form-group">
                    <label>检验员：</label>
                    <input type="text" id="batch-inspector" placeholder="留空表示不修改">
                </div>
                <div class="form-group">
                    <label>备注：</label>
                    <textarea id="batch-remarks" placeholder="留空表示不修改"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeBatchEditModal()">取消</button>
            <button type="button" class="btn btn-primary" onclick="submitBatchEdit()">确认修改</button>
        </div>
    </div>
</div>

<!-- Toast 提示 -->
<div id="toast" class="toast"></div>
{% endblock %}

{% block extra_js %}
<script>
    const inspectionType = '{{ inspection_type }}';
    let currentPage = 1;
    let currentFilters = {};

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadData();
        setupSearch();
        initializeCheckboxEvents();
    });

    // 设置搜索功能
    function setupSearch() {
        const searchInput = document.getElementById('quick-search-input');
        const searchIcon = document.getElementById('search-icon');

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        searchIcon.addEventListener('click', performSearch);
    }

    function performSearch() {
        const searchTerm = document.getElementById('quick-search-input').value.trim();
        currentFilters.search = searchTerm;
        currentPage = 1;
        loadData();
    }

    async function loadData(page = 1) {
        showLoading();

        try {
            const params = new URLSearchParams({
                type: inspectionType,
                page: page,
                per_page: 20,
                ...currentFilters
            });

            const response = await fetch(`/pending_inspection/api/pending_inspections?${params}`);
            const data = await response.json();

            if (data.success) {
                renderData(data.data);
                updatePagination(data.data);
            } else {
                showToast('加载数据失败: ' + data.error, 'error');
                showEmptyState();
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            showToast('加载数据失败，请重试', 'error');
            showEmptyState();
        } finally {
            hideLoading();
        }
    }

    function renderData(data) {
        const tbody = document.getElementById('data-tbody');

        if (!data.items || data.items.length === 0) {
            // 表格始终显示，但tbody为空，显示空状态消息
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" style="text-align: center; padding: 40px; color: #666;">
                        暂无待检物料，点击"批量导入"开始添加
                    </td>
                </tr>
            `;
            document.getElementById('empty-state').style.display = 'none';
            return;
        }

        let html = '';
        data.items.forEach(item => {
            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="row-checkbox" value="${item.id}" data-item='${JSON.stringify(item)}'>
                    </td>
                    <td>${item.material_code || ''}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.specification || ''}</td>
                    <td>${item.supplier_name || ''}</td>
                    <td>${item.incoming_quantity || ''}</td>
                    <td>${item.unit || ''}</td>
                    <td>${item.batch_number || ''}</td>
                    <td>${item.created_at || ''}</td>
                    <td>${item.inspector || ''}</td>
                    <td>${getActionButtons(item)}</td>
                </tr>
            `;
        });

        tbody.innerHTML = html;
        document.getElementById('empty-state').style.display = 'none';
    }



    function getActionButtons(item) {
        // 待检清单中只保留开始检验按钮，编辑和删除通过批量操作实现
        const buttons = `
            <button type="button" class="btn btn-success btn-sm" onclick="startInspection(${item.id})" title="开始检验">
                开始检验
            </button>
        `;

        return `<div class="action-buttons">${buttons}</div>`;
    }

    function updatePagination(data) {
        const container = document.getElementById('pagination-container');
        const summary = document.getElementById('summary');
        const pagination = document.getElementById('pagination');

        if (!data.items || data.items.length === 0) {
            container.style.display = 'none';
            return;
        }

        // 更新摘要信息
        const start = (data.page - 1) * data.per_page + 1;
        const end = Math.min(data.page * data.per_page, data.total);
        summary.textContent = `显示第 ${start}-${end} 条，共 ${data.total} 条记录`;

        // 生成分页按钮
        let paginationHtml = '';

        // 上一页
        if (data.page > 1) {
            paginationHtml += `<a href="#" onclick="loadData(${data.page - 1}); return false;">上一页</a>`;
        } else {
            paginationHtml += `<span class="disabled">上一页</span>`;
        }

        // 页码
        for (let i = Math.max(1, data.page - 2); i <= Math.min(data.pages, data.page + 2); i++) {
            if (i === data.page) {
                paginationHtml += `<span class="active">${i}</span>`;
            } else {
                paginationHtml += `<a href="#" onclick="loadData(${i}); return false;">${i}</a>`;
            }
        }

        // 下一页
        if (data.page < data.pages) {
            paginationHtml += `<a href="#" onclick="loadData(${data.page + 1}); return false;">下一页</a>`;
        } else {
            paginationHtml += `<span class="disabled">下一页</span>`;
        }

        pagination.innerHTML = paginationHtml;
        container.style.display = 'flex';
    }

    /* 移除resetFilters函数，不再需要筛选重置功能 */

    async function startInspection(itemId) {
        if (!confirm('确定要开始检验吗？')) {
            return;
        }

        try {
            const response = await fetch(`/pending_inspection/api/pending_inspections/${itemId}/start_inspection`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                showToast('检验记录创建成功', 'success');
                const editUrl = data.data.inspection_type === 'sampling'
                    ? `/sampling_inspection/edit/${data.data.inspection_record_id}`
                    : `/full_inspection/edit/${data.data.inspection_record_id}`;
                setTimeout(() => window.location.href = editUrl, 1000);
            } else {
                showToast('创建检验记录失败: ' + data.error, 'error');
            }
        } catch (error) {
            showToast('开始检验失败，请重试', 'error');
        }
    }

    function editItem(itemId) {
        showToast('编辑功能开发中', 'warning');
    }

    async function deleteItem(itemId) {
        if (!confirm('确定要删除这个待检物料吗？')) {
            return;
        }

        try {
            const response = await fetch(`/pending_inspection/api/pending_inspections/${itemId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                showToast('删除成功', 'success');
                loadData(currentPage);
            } else {
                showToast('删除失败: ' + data.error, 'error');
            }
        } catch (error) {
            showToast('删除失败，请重试', 'error');
        }
    }

    // 工具函数
    function showLoading() {
        // 只在表格内显示加载状态，保持表头可见
        const tbody = document.getElementById('data-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="10" style="text-align: center; padding: 40px; color: #666;">
                    正在加载数据...
                </td>
            </tr>
        `;
        document.getElementById('empty-state').style.display = 'none';
    }

    function hideLoading() {
        // 不需要隐藏独立的加载元素，因为已经移除了
    }

    function showEmptyState() {
        // 表格始终显示，在tbody中显示空状态
        const tbody = document.getElementById('data-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="10" style="text-align: center; padding: 40px; color: #666;">
                    暂无待检物料，点击"批量导入"开始添加
                </td>
            </tr>
        `;
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('pagination-container').style.display = 'none';
    }

    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type} show`;

        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // 复选框和批量操作相关函数
    function initializeCheckboxEvents() {
        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const rowCheckboxes = document.querySelectorAll('.row-checkbox');
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                    // 更新行的选中状态样式
                    const row = checkbox.closest('tr');
                    if (this.checked) {
                        row.classList.add('selected');
                    } else {
                        row.classList.remove('selected');
                    }
                });
                updateBatchActionsVisibility();
            });
        }

        // 监听行复选框变化
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('row-checkbox')) {
                // 更新行的选中状态样式
                const row = e.target.closest('tr');
                if (e.target.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }

                updateSelectAllState();
                updateBatchActionsVisibility();
            }
        });

        // 批量编辑按钮
        const batchEditBtn = document.getElementById('batch-edit-btn');
        if (batchEditBtn) {
            batchEditBtn.addEventListener('click', batchEdit);
        }

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batch-delete-btn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', batchDelete);
        }
    }

    function updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('select-all');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

        if (checkedBoxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedBoxes.length === rowCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    function updateBatchActionsVisibility() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        const batchActions = document.getElementById('batch-actions');

        if (checkedBoxes.length > 0) {
            batchActions.style.display = 'flex';
        } else {
            batchActions.style.display = 'none';
        }
    }

    function getSelectedItems() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        const selectedItems = [];

        checkedBoxes.forEach(checkbox => {
            try {
                const itemData = JSON.parse(checkbox.getAttribute('data-item'));
                selectedItems.push(itemData);
            } catch (e) {
                console.error('解析选中项数据失败:', e);
            }
        });

        return selectedItems;
    }

    function batchEdit() {
        const selectedItems = getSelectedItems();
        if (selectedItems.length === 0) {
            showToast('请先选择要编辑的项目', 'warning');
            return;
        }

        // 显示批量编辑模态框
        document.getElementById('selected-count').textContent = selectedItems.length;
        document.getElementById('batch-edit-modal').style.display = 'flex';

        // 清空表单
        document.getElementById('batch-supplier').value = '';
        document.getElementById('batch-inspector').value = '';
        document.getElementById('batch-remarks').value = '';
    }

    function closeBatchEditModal() {
        document.getElementById('batch-edit-modal').style.display = 'none';
    }

    async function submitBatchEdit() {
        const selectedItems = getSelectedItems();
        if (selectedItems.length === 0) {
            showToast('没有选中的项目', 'warning');
            return;
        }

        // 获取表单数据
        const supplier = document.getElementById('batch-supplier').value.trim();
        const inspector = document.getElementById('batch-inspector').value.trim();
        const remarks = document.getElementById('batch-remarks').value.trim();

        // 构建更新数据
        const updateData = {};
        if (supplier) updateData.supplier_name = supplier;
        if (inspector) updateData.inspector = inspector;
        if (remarks) updateData.remarks = remarks;

        if (Object.keys(updateData).length === 0) {
            showToast('请至少填写一个要修改的字段', 'warning');
            return;
        }

        try {
            showLoading();

            // 批量更新API调用
            const updatePromises = selectedItems.map(item =>
                fetch(`/pending_inspection/api/pending_inspections/${item.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                })
            );

            const results = await Promise.all(updatePromises);
            const successCount = results.filter(r => r.ok).length;

            if (successCount === selectedItems.length) {
                showToast(`成功更新 ${successCount} 个项目`, 'success');
            } else {
                showToast(`更新完成，成功 ${successCount} 个，失败 ${selectedItems.length - successCount} 个`, 'warning');
            }

            // 关闭模态框
            closeBatchEditModal();

            // 重新加载数据
            loadData(currentPage);

            // 清除选择状态
            document.getElementById('select-all').checked = false;
            updateBatchActionsVisibility();

        } catch (error) {
            console.error('批量编辑失败:', error);
            showToast('批量编辑失败，请重试', 'error');
        } finally {
            hideLoading();
        }
    }

    async function batchDelete() {
        const selectedItems = getSelectedItems();
        if (selectedItems.length === 0) {
            showToast('请先选择要删除的项目', 'warning');
            return;
        }

        if (!confirm(`确定要删除选中的 ${selectedItems.length} 个项目吗？此操作不可撤销。`)) {
            return;
        }

        try {
            showLoading();

            // 批量删除API调用
            const deletePromises = selectedItems.map(item =>
                fetch(`/pending_inspection/api/pending_inspections/${item.id}`, {
                    method: 'DELETE'
                })
            );

            const results = await Promise.all(deletePromises);
            const successCount = results.filter(r => r.ok).length;

            if (successCount === selectedItems.length) {
                showToast(`成功删除 ${successCount} 个项目`, 'success');
            } else {
                showToast(`删除完成，成功 ${successCount} 个，失败 ${selectedItems.length - successCount} 个`, 'warning');
            }

            // 重新加载数据
            loadData(currentPage);

            // 清除选择状态
            document.getElementById('select-all').checked = false;
            updateBatchActionsVisibility();

        } catch (error) {
            console.error('批量删除失败:', error);
            showToast('批量删除失败，请重试', 'error');
        } finally {
            hideLoading();
        }
    }


</script>
{% endblock %}
